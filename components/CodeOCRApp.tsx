"use client";

import ImageUpload from "@/components/ImageUpload";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import type { BundledLanguage } from "@/components/ui/kibo-ui/code-block";
import {
  CodeBlock,
  CodeBlockBody,
  CodeBlockContent,
  CodeBlockCopyButton,
  CodeBlockFilename,
  CodeBlockHeader,
  CodeBlockItem,
} from "@/components/ui/kibo-ui/code-block";
import { Loader2, ScanText } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { CodeBlockFiles } from "@/components/ui/kibo-ui/code-block";
interface CodeOCRResponse {
  language: string;
  code: string;
  status: string;
}

export default function CodeOCRApp() {
  const t = useTranslations("CodeOCR");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<CodeOCRResponse | null>(null);

  const handleFileSelect = useCallback((file: File) => {
    setSelectedFile(file);
    setResult(null);
  }, []);

  const handleRemoveFile = useCallback(() => {
    setSelectedFile(null);
    setResult(null);
  }, []);

  // Helper function to normalize language names for Shiki
  const normalizeLanguage = (language: string): string => {
    const languageMap: Record<string, string> = {
      Python: "python",
      JavaScript: "javascript",
      TypeScript: "typescript",
      Java: "java",
      "C++": "cpp",
      "C#": "csharp",
      C: "c",
      Go: "go",
      Rust: "rust",
      PHP: "php",
      Ruby: "ruby",
      Swift: "swift",
      Kotlin: "kotlin",
      Scala: "scala",
      HTML: "html",
      CSS: "css",
      SQL: "sql",
      Shell: "bash",
      Bash: "bash",
      PowerShell: "powershell",
      R: "r",
      MATLAB: "matlab",
      Perl: "perl",
      Lua: "lua",
      Dart: "dart",
      "Objective-C": "objective-c",
    };

    return languageMap[language] || language.toLowerCase();
  };

  const processImage = async () => {
    if (!selectedFile) {
      toast.error(t("selectImageFirst"));
      return;
    }

    setIsProcessing(true);

    try {
      const formData = new FormData();
      formData.append("file", selectedFile);

      const response = await fetch("/api/v1/codeocr", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || errorData.detail || t("extractFailed")
        );
      }

      const data: CodeOCRResponse = await response.json();
      // Normalize the language for Shiki
      const normalizedData = {
        ...data,
        language: normalizeLanguage(data.language),
      };
      setResult(normalizedData);
      toast.success(t("extractSuccess"));
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : t("extractFailed");
      toast.error(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-4">
      <Card className="shadow-md">
        <CardContent className="p-4">
          <div className="space-y-4">
            <ImageUpload
              onFileSelect={handleFileSelect}
              onRemoveFile={handleRemoveFile}
              selectedFile={selectedFile}
              disabled={isProcessing}
            />

            <Button
              onClick={processImage}
              disabled={!selectedFile || isProcessing}
              className="w-full"
              size="lg"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("processing")}
                </>
              ) : (
                <>
                  <ScanText className="mr-2 h-4 w-4" />
                  {t("extractCode")}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {result && result.code && (
        <CodeBlock
          data={[
            {
              language: result.language,
              filename: result.language,
              code: result.code,
            },
          ]}
          defaultValue={result.language}
        >
          <CodeBlockHeader>
            <CodeBlockFiles>
              {(item) => (
                <CodeBlockFilename key={item.language} value={item.language}>
                  {item.filename}
                </CodeBlockFilename>
              )}
            </CodeBlockFiles>
            <CodeBlockCopyButton />
          </CodeBlockHeader>
          <CodeBlockBody>
            {(item) => (
              <CodeBlockItem key={item.language} value={item.language}>
                <CodeBlockContent language={item.language as BundledLanguage}>
                  {item.code}
                </CodeBlockContent>
              </CodeBlockItem>
            )}
          </CodeBlockBody>
        </CodeBlock>
      )}

      {/* No code message */}
      {result && !result.code && (
        <div className="border rounded-lg p-8 text-center text-gray-500 dark:text-gray-400">
          No code to display
        </div>
      )}
    </div>
  );
}
